/* Import accessibility and responsive styles */
@import './styles/accessibility.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for HTML summary content */
@layer components {
  .bill-summary-content {
    @apply text-gray-700 leading-relaxed;
  }

  .bill-summary-content h1,
  .bill-summary-content h2,
  .bill-summary-content h3,
  .bill-summary-content h4,
  .bill-summary-content h5,
  .bill-summary-content h6 {
    @apply font-bold text-gray-900 mt-4 mb-2;
  }

  .bill-summary-content h1 { @apply text-2xl; }
  .bill-summary-content h2 { @apply text-xl; }
  .bill-summary-content h3 { @apply text-lg; }
  .bill-summary-content h4 { @apply text-base; }

  .bill-summary-content p {
    @apply mb-3 text-gray-700;
  }

  .bill-summary-content strong {
    @apply font-semibold text-gray-900;
  }

  .bill-summary-content em {
    @apply italic text-gray-600;
  }

  .bill-summary-content ul,
  .bill-summary-content ol {
    @apply ml-4 mb-3;
  }

  .bill-summary-content li {
    @apply mb-1;
  }

  .bill-summary-content ul li {
    @apply list-disc;
  }

  .bill-summary-content ol li {
    @apply list-decimal;
  }
}
/* Mobile responsiveness utilities */
@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* Ensure proper touch targets on mobile */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Safe area padding for mobile devices */
  .safe-area-padding {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
  
  /* Prevent horizontal scroll on mobile */
  .prevent-horizontal-scroll {
    overflow-x: hidden;
    max-width: 100vw;
  }
}

/* Mobile-specific styles */
@media (max-width: 640px) {
  /* Ensure sidebar doesn't cause horizontal scroll */
  .sidebar-mobile {
    width: 100vw;
    max-width: 280px;
  }
  
  /* Adjust font sizes for better mobile readability */
  .mobile-text-adjust h1 {
    font-size: 1.5rem;
    line-height: 1.4;
  }
  
  .mobile-text-adjust h2 {
    font-size: 1.25rem;
    line-height: 1.4;
  }
  
  .mobile-text-adjust h3 {
    font-size: 1.125rem;
    line-height: 1.4;
  }
}

/* Ensure proper spacing on very small screens */
@media (max-width: 375px) {
  .container-mobile {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}