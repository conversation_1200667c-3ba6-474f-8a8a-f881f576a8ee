/**
 * Accessibility and Responsive Styles
 * Additional styles for improved accessibility and mobile responsiveness
 */

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only.focus:not(.sr-only) {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Focus styles for better visibility */
.focus-visible:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-blue-50 {
    background-color: #ffffff;
    border: 2px solid #000000;
  }
  
  .text-gray-600 {
    color: #000000;
  }
  
  .border-gray-200 {
    border-color: #000000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animate-pulse,
  .animate-spin,
  .transition-all,
  .transition-colors,
  .transition-transform {
    animation: none;
    transition: none;
  }
  
  .group-hover\:translate-x-1:hover {
    transform: none;
  }
}

/* Touch target improvements for mobile */
@media (max-width: 768px) {
  button,
  [role="button"],
  a {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  /* Larger tap targets for mobile */
  .mobile-tap-target {
    padding: 12px;
    margin: 4px;
  }
}

/* Improved focus indicators */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 2px #ffffff, 0 0 0 4px #3b82f6;
}

/* Skip links */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #3b82f6;
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}

/* Accessible form styles */
.form-error {
  color: #dc2626;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.form-error::before {
  content: "⚠ ";
  font-weight: bold;
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@media (prefers-reduced-motion: reduce) {
  .loading-skeleton {
    animation: none;
    background: #f3f4f6;
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Responsive text sizes */
@media (max-width: 640px) {
  .responsive-text-lg {
    font-size: 1rem;
    line-height: 1.5rem;
  }
  
  .responsive-text-xl {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
  
  .responsive-text-2xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

/* Card hover states for better interaction feedback */
.interactive-card {
  transition: box-shadow 0.2s ease-in-out, transform 0.1s ease-in-out;
}

.interactive-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.interactive-card:focus-within {
  box-shadow: 0 0 0 2px #3b82f6;
}

@media (prefers-reduced-motion: reduce) {
  .interactive-card {
    transition: none;
  }
  
  .interactive-card:hover {
    transform: none;
  }
}

/* Accessible dropdown styles */
.dropdown-menu {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
}

.dropdown-menu.open {
  max-height: 400px;
}

@media (prefers-reduced-motion: reduce) {
  .dropdown-menu {
    transition: none;
  }
  
  .dropdown-menu.open {
    max-height: none;
  }
}

/* Status indicators with better contrast */
.status-indicator {
  display: inline-flex;
  align-items: center;
  font-weight: 500;
  border-radius: 9999px;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  line-height: 1rem;
}

.status-submitted {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.status-under-review {
  background-color: #fef3c7;
  color: #92400e;
  border: 1px solid #f59e0b;
}

.status-in-progress {
  background-color: #dbeafe;
  color: #1e40af;
  border: 1px solid #3b82f6;
}

.status-resolved {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #10b981;
}

/* Mobile-first responsive grid */
.responsive-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .responsive-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .responsive-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break-before {
    page-break-before: always;
  }
  
  .print-break-after {
    page-break-after: always;
  }
}

/* Dashboard Layout Fixes */
.dashboard-layout {
  display: flex;
  min-height: 100vh;
}

.dashboard-sidebar {
  flex-shrink: 0;
}

.dashboard-main {
  flex: 1;
  min-width: 0; /* Prevents flex item from overflowing */
}

/* Ensure proper spacing for fixed header and sidebar */
.dashboard-content {
  padding-top: 1rem;
  width: 100%;
  max-width: none;
}

/* Mobile adjustments */
@media (max-width: 1023px) {
  .dashboard-main {
    margin-left: 0 !important;
  }
}

/* Desktop adjustments */
@media (min-width: 1024px) {
  .dashboard-main {
    margin-left: 16rem; /* 64 * 0.25rem = 16rem for w-64 sidebar */
    width: calc(100% - 16rem);
  }

  .dashboard-main.sidebar-collapsed {
    margin-left: 4rem; /* 16 * 0.25rem = 4rem for w-16 sidebar */
    width: calc(100% - 4rem);
  }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
  .dark-mode-support {
    background-color: #1f2937;
    color: #f9fafb;
  }

  .dark-mode-support .bg-white {
    background-color: #374151;
  }

  .dark-mode-support .text-gray-900 {
    color: #f9fafb;
  }

  .dark-mode-support .border-gray-200 {
    border-color: #4b5563;
  }
}
