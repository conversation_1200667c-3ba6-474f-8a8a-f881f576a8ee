# Generated by Django 4.2.23 on 2025-08-29 11:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('projects', '0006_alter_bill_options_and_more'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='project',
            name='projects_pr_status_af6027_idx',
        ),
        migrations.RemoveIndex(
            model_name='project',
            name='projects_pr_project_573ce6_idx',
        ),
        migrations.RemoveIndex(
            model_name='project',
            name='projects_pr_start_d_9f6d89_idx',
        ),
        migrations.RemoveField(
            model_name='project',
            name='budget',
        ),
        migrations.RemoveField(
            model_name='project',
            name='end_date',
        ),
        migrations.RemoveField(
            model_name='project',
            name='image',
        ),
        migrations.RemoveField(
            model_name='project',
            name='implementing_ministry',
        ),
        migrations.RemoveField(
            model_name='project',
            name='project_type',
        ),
        migrations.RemoveField(
            model_name='project',
            name='public_participation_open',
        ),
        migrations.RemoveField(
            model_name='project',
            name='start_date',
        ),
        migrations.RemoveField(
            model_name='project',
            name='status',
        ),
        migrations.RemoveField(
            model_name='project',
            name='target_beneficiaries',
        ),
        migrations.AddField(
            model_name='project',
            name='sponsor',
            field=models.CharField(default='Ministry of Public Works', help_text='Project sponsor (Ministry/Department)', max_length=200),
        ),
        migrations.AddField(
            model_name='project',
            name='summary',
            field=models.TextField(blank=True, help_text='Auto-generated summary from document'),
        ),
        migrations.AddIndex(
            model_name='project',
            index=models.Index(fields=['participation_deadline'], name='projects_pr_partici_5f115e_idx'),
        ),
    ]
