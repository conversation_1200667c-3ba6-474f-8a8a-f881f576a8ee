# Generated by Django 4.2.23 on 2025-08-24 16:04

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('users', '0002_alter_customuser_managers'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('feedback', '0002_feedback_can_delete_feedback_can_edit_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Project',
            fields=[
                ('is_deleted', models.BooleanField(db_index=True, default=False)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.Char<PERSON><PERSON>(max_length=200)),
                ('description', models.TextField()),
                ('project_type', models.CharField(choices=[('infrastructure', 'Infrastructure'), ('healthcare', 'Healthcare'), ('education', 'Education'), ('agriculture', 'Agriculture'), ('environment', 'Environment'), ('social', 'Social Services')], max_length=20)),
                ('status', models.CharField(choices=[('planning', 'Planning'), ('approved', 'Approved'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='planning', max_length=20)),
                ('budget', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True)),
                ('start_date', models.DateField(blank=True, null=True)),
                ('end_date', models.DateField(blank=True, null=True)),
                ('county', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='projects', to='users.county')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='deleted_%(class)s_set', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='AdminFeedbackResponse',
            fields=[
                ('is_deleted', models.BooleanField(db_index=True, default=False)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('response_text', models.TextField()),
                ('response_date', models.DateTimeField(auto_now_add=True)),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='deleted_%(class)s_set', to=settings.AUTH_USER_MODEL)),
                ('feedback', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='admin_response', to='feedback.feedback')),
                ('responded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='admin_responses', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
