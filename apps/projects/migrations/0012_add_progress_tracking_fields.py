# Generated by Django 4.2.23 on 2025-08-30 01:39

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("projects", "0011_remove_bill_extraction_method_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="BillChunk",
            fields=[
                ("is_deleted", models.BooleanField(db_index=True, default=False)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "chunk_index",
                    models.IntegerField(help_text="Order of chunk in bill (0-based)"),
                ),
                (
                    "section_title",
                    models.CharField(
                        blank=True,
                        help_text="Section title if applicable",
                        max_length=200,
                    ),
                ),
                ("content", models.TextField(help_text="Raw text content of chunk")),
                (
                    "processed_content",
                    models.TextField(blank=True, help_text="AI-processed content"),
                ),
                ("character_count", models.IntegerField(default=0)),
                ("word_count", models.IntegerField(default=0)),
                (
                    "start_position",
                    models.IntegerField(
                        default=0, help_text="Character position in original document"
                    ),
                ),
                (
                    "end_position",
                    models.IntegerField(
                        default=0,
                        help_text="End character position in original document",
                    ),
                ),
                ("is_processed", models.BooleanField(default=False)),
                ("processing_error", models.TextField(blank=True)),
            ],
            options={
                "ordering": ["bill", "chunk_index"],
            },
        ),
        migrations.AddField(
            model_name="bill",
            name="estimated_time_remaining",
            field=models.IntegerField(
                blank=True, help_text="Estimated time remaining in seconds", null=True
            ),
        ),
        migrations.AddField(
            model_name="bill",
            name="is_chunked",
            field=models.BooleanField(
                default=False, help_text="Whether bill has been chunked for chat"
            ),
        ),
        migrations.AddField(
            model_name="bill",
            name="processing_message",
            field=models.CharField(
                blank=True, help_text="Current processing stage message", max_length=200
            ),
        ),
        migrations.AddField(
            model_name="bill",
            name="processing_progress",
            field=models.IntegerField(
                default=0, help_text="Processing progress percentage (0-100)"
            ),
        ),
        migrations.AddField(
            model_name="bill",
            name="processing_status",
            field=models.CharField(
                choices=[
                    ("pending", "Pending"),
                    ("processing", "Processing"),
                    ("completed", "Completed"),
                    ("failed", "Failed"),
                ],
                default="pending",
                help_text="Current processing status",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="bill",
            name="summary_html",
            field=models.TextField(blank=True, help_text="HTML formatted summary"),
        ),
        migrations.AddField(
            model_name="bill",
            name="total_chunks",
            field=models.IntegerField(
                default=0, help_text="Total number of chunks created"
            ),
        ),
        migrations.AddIndex(
            model_name="bill",
            index=models.Index(
                fields=["processing_status"], name="projects_bi_process_8df002_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="bill",
            index=models.Index(fields=["status"], name="projects_bi_status_917218_idx"),
        ),
        migrations.AddField(
            model_name="billchunk",
            name="bill",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="chunks",
                to="projects.bill",
            ),
        ),
        migrations.AddField(
            model_name="billchunk",
            name="deleted_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="deleted_%(class)s_set",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddIndex(
            model_name="billchunk",
            index=models.Index(
                fields=["bill", "chunk_index"], name="projects_bi_bill_id_5168a1_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="billchunk",
            index=models.Index(
                fields=["bill", "is_processed"], name="projects_bi_bill_id_bab771_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="billchunk",
            unique_together={("bill", "chunk_index")},
        ),
    ]
