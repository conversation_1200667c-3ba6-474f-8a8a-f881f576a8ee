# Generated by Django 4.2.23 on 2025-08-29 11:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('projects', '0005_alter_project_options_alter_bill_managers_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='bill',
            options={'ordering': ['-created_at']},
        ),
        migrations.RemoveIndex(
            model_name='bill',
            name='projects_bi_status_07c63a_idx',
        ),
        migrations.RemoveIndex(
            model_name='bill',
            name='projects_bi_bill_nu_9ea1b5_idx',
        ),
        migrations.RemoveIndex(
            model_name='bill',
            name='projects_bi_introdu_e665ec_idx',
        ),
        migrations.RemoveField(
            model_name='bill',
            name='bill_number',
        ),
        migrations.RemoveField(
            model_name='bill',
            name='committee',
        ),
        migrations.RemoveField(
            model_name='bill',
            name='committee_deadline',
        ),
        migrations.RemoveField(
            model_name='bill',
            name='first_reading_date',
        ),
        migrations.RemoveField(
            model_name='bill',
            name='full_text',
        ),
        migrations.RemoveField(
            model_name='bill',
            name='image',
        ),
        migrations.RemoveField(
            model_name='bill',
            name='introduced_date',
        ),
        migrations.RemoveField(
            model_name='bill',
            name='public_participation_open',
        ),
        migrations.RemoveField(
            model_name='bill',
            name='status',
        ),
        migrations.AlterField(
            model_name='bill',
            name='summary',
            field=models.TextField(blank=True, help_text='Auto-generated summary from document'),
        ),
        migrations.AddIndex(
            model_name='bill',
            index=models.Index(fields=['participation_deadline'], name='projects_bi_partici_cfa59e_idx'),
        ),
    ]
