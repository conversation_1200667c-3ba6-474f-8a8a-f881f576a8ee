# Generated by Django 4.2.23 on 2025-09-01 17:53

from django.db import migrations, models
import pgvector.django.vector


class Migration(migrations.Migration):

    dependencies = [
        ('projects', '0014_alter_billchunk_embedding'),
    ]

    operations = [
        migrations.AddField(
            model_name='billchunk',
            name='embedding_created_at',
            field=models.DateTimeField(blank=True, help_text='When embedding was generated', null=True),
        ),
        migrations.AddField(
            model_name='billchunk',
            name='embedding_dimensions',
            field=models.IntegerField(blank=True, help_text='Embedding vector dimensions', null=True),
        ),
        migrations.AddField(
            model_name='billchunk',
            name='embedding_error',
            field=models.TextField(blank=True, help_text='Error message if embedding generation failed'),
        ),
        migrations.AddField(
            model_name='billchunk',
            name='embedding_model',
            field=models.CharField(blank=True, help_text='Model used for embedding', max_length=50),
        ),
        migrations.AddField(
            model_name='billchunk',
            name='is_placeholder_embedding',
            field=models.BooleanField(default=False, help_text='Whether this is a placeholder embedding'),
        ),
        migrations.AlterField(
            model_name='billchunk',
            name='embedding',
            field=pgvector.django.vector.VectorField(blank=True, dimensions=1536, help_text='Vector embedding for similarity search', null=True),
        ),
    ]
