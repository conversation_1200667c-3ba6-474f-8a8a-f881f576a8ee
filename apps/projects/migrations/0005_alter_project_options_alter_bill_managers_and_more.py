# Generated by Django 4.2.23 on 2025-08-25 12:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('projects', '0004_merge_20250825_1545'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='project',
            options={'ordering': ['-created_at']},
        ),
        migrations.AlterModelManagers(
            name='bill',
            managers=[
            ],
        ),
        migrations.AddIndex(
            model_name='bill',
            index=models.Index(fields=['status', 'public_participation_open'], name='projects_bi_status_07c63a_idx'),
        ),
        migrations.AddIndex(
            model_name='bill',
            index=models.Index(fields=['bill_number'], name='projects_bi_bill_nu_9ea1b5_idx'),
        ),
        migrations.AddIndex(
            model_name='bill',
            index=models.Index(fields=['introduced_date'], name='projects_bi_introdu_e665ec_idx'),
        ),
        migrations.AddIndex(
            model_name='project',
            index=models.Index(fields=['status', 'public_participation_open'], name='projects_pr_status_af6027_idx'),
        ),
        migrations.AddIndex(
            model_name='project',
            index=models.Index(fields=['project_type'], name='projects_pr_project_573ce6_idx'),
        ),
        migrations.AddIndex(
            model_name='project',
            index=models.Index(fields=['start_date'], name='projects_pr_start_d_9f6d89_idx'),
        ),
    ]
