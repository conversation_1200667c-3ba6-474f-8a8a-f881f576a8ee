# Generated by Django 4.2.23 on 2025-08-31 14:51

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("projects", "0012_add_progress_tracking_fields"),
    ]

    operations = [
        migrations.AddField(
            model_name="billchunk",
            name="embedding",
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.FloatField(),
                blank=True,
                help_text="Vector embedding for similarity search",
                null=True,
                size=None,
            ),
        ),
    ]
