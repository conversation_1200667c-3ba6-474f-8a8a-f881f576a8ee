# Generated by Django 4.2.23 on 2025-08-25 12:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('feedback', '0003_merge_20250825_1545'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='feedback',
            name='feedback_fe_county__55692a_idx',
        ),
        migrations.RemoveIndex(
            model_name='feedback',
            name='feedback_fe_is_anon_db6bfb_idx',
        ),
        migrations.RemoveIndex(
            model_name='feedback',
            name='feedback_fe_created_eb9d6b_idx',
        ),
        migrations.AddIndex(
            model_name='feedback',
            index=models.Index(fields=['user_county', 'status'], name='feedback_fe_user_co_bf5a78_idx'),
        ),
        migrations.AddIndex(
            model_name='feedback',
            index=models.Index(fields=['is_anonymous', 'user_county'], name='feedback_fe_is_anon_ad63b6_idx'),
        ),
        migrations.AddIndex(
            model_name='feedback',
            index=models.Index(fields=['created_at', 'status'], name='feedback_fe_created_9e3e55_idx'),
        ),
        migrations.AddIndex(
            model_name='feedback',
            index=models.Index(fields=['related_bill_id'], name='feedback_fe_related_ed6c63_idx'),
        ),
        migrations.AddIndex(
            model_name='feedback',
            index=models.Index(fields=['related_project_id'], name='feedback_fe_related_990dd9_idx'),
        ),
    ]
