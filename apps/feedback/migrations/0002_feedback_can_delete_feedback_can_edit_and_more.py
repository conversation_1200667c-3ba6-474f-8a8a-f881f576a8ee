# Generated by Django 4.2.23 on 2025-08-08 21:25

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('feedback', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='feedback',
            name='can_delete',
            field=models.BooleanField(default=True, help_text='Admin can disable deletion'),
        ),
        migrations.AddField(
            model_name='feedback',
            name='can_edit',
            field=models.BooleanField(default=True, help_text='Admin can disable editing'),
        ),
        migrations.AddField(
            model_name='feedback',
            name='edit_count',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='feedback',
            name='edited_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name='FeedbackEdit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_deleted', models.BooleanField(db_index=True, default=False)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('previous_title', models.CharField(max_length=200)),
                ('previous_content', models.TextField()),
                ('previous_category', models.CharField(max_length=20)),
                ('edit_reason', models.CharField(blank=True, max_length=200)),
                ('edited_at', models.DateTimeField(auto_now_add=True)),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='deleted_%(class)s_set', to=settings.AUTH_USER_MODEL)),
                ('feedback', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='edit_history', to='feedback.feedback')),
            ],
            options={
                'ordering': ['-edited_at'],
            },
        ),
    ]
