# Generated by Django 4.2.23 on 2025-08-07 14:32

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('users', '0002_alter_customuser_managers'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Feedback',
            fields=[
                ('is_deleted', models.BooleanField(db_index=True, default=False)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(db_index=True, max_length=200)),
                ('content', models.TextField()),
                ('category', models.CharField(choices=[('infrastructure', 'Infrastructure & Roads'), ('healthcare', 'Healthcare Services'), ('education', 'Education & Schools'), ('water_sanitation', 'Water & Sanitation'), ('security', 'Security & Safety'), ('environment', 'Environment & Waste'), ('governance', 'Governance & Corruption'), ('economic', 'Economic Development'), ('other', 'Other Issues')], db_index=True, max_length=20)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('urgent', 'Urgent')], default='medium', max_length=10)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('in_review', 'In Review'), ('responded', 'Responded'), ('resolved', 'Resolved'), ('closed', 'Closed')], db_index=True, default='pending', max_length=15)),
                ('tracking_id', models.CharField(db_index=True, max_length=16, unique=True)),
                ('is_anonymous', models.BooleanField(db_index=True, default=False)),
                ('submitted_via', models.CharField(choices=[('web', 'Web'), ('mobile', 'Mobile'), ('api', 'API')], default='web', max_length=10)),
                ('response_count', models.IntegerField(default=0)),
                ('last_response_at', models.DateTimeField(blank=True, null=True)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('view_count', models.IntegerField(default=0)),
                ('sentiment_score', models.FloatField(blank=True, null=True)),
                ('county', models.ForeignKey(help_text='Tenant boundary - determines data access', on_delete=django.db.models.deletion.CASCADE, related_name='feedback_items', to='users.county')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='deleted_%(class)s_set', to=settings.AUTH_USER_MODEL)),
                ('sub_county', models.ForeignKey(blank=True, limit_choices_to={'type': 'sub_county'}, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='feedback_sub_county', to='users.location')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='feedback_submissions', to=settings.AUTH_USER_MODEL)),
                ('village', models.ForeignKey(blank=True, limit_choices_to={'type': 'village'}, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='feedback_village', to='users.location')),
                ('ward', models.ForeignKey(blank=True, limit_choices_to={'type': 'ward'}, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='feedback_ward', to='users.location')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='FeedbackResponse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_deleted', models.BooleanField(db_index=True, default=False)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('content', models.TextField()),
                ('is_public', models.BooleanField(default=True)),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='deleted_%(class)s_set', to=settings.AUTH_USER_MODEL)),
                ('feedback', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='responses', to='feedback.feedback')),
                ('responder', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='feedback_responses', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='feedback',
            index=models.Index(fields=['county', 'status'], name='feedback_fe_county__55692a_idx'),
        ),
        migrations.AddIndex(
            model_name='feedback',
            index=models.Index(fields=['user', 'created_at'], name='feedback_fe_user_id_be0124_idx'),
        ),
        migrations.AddIndex(
            model_name='feedback',
            index=models.Index(fields=['tracking_id'], name='feedback_fe_trackin_9d4d63_idx'),
        ),
        migrations.AddIndex(
            model_name='feedback',
            index=models.Index(fields=['category', 'priority'], name='feedback_fe_categor_4ef27f_idx'),
        ),
        migrations.AddIndex(
            model_name='feedback',
            index=models.Index(fields=['is_anonymous', 'county'], name='feedback_fe_is_anon_db6bfb_idx'),
        ),
        migrations.AddIndex(
            model_name='feedback',
            index=models.Index(fields=['created_at', 'county'], name='feedback_fe_created_eb9d6b_idx'),
        ),
    ]
