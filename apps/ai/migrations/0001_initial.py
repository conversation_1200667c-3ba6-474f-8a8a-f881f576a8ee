# Generated by Django 4.2.23 on 2025-08-09 16:18

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('users', '0002_alter_customuser_managers'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('feedback', '0002_feedback_can_delete_feedback_can_edit_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='FeedbackAIAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_deleted', models.BooleanField(db_index=True, default=False)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('sentiment_score', models.Float<PERSON>ield(blank=True, help_text='Sentiment score from -1.0 (negative) to 1.0 (positive)', null=True)),
                ('sentiment_label', models.CharField(blank=True, choices=[('negative', 'Negative'), ('neutral', 'Neutral'), ('positive', 'Positive')], help_text='Classified sentiment category', max_length=20, null=True)),
                ('emotion_detected', models.CharField(blank=True, help_text='Specific emotion detected (angry, frustrated, hopeful, etc.)', max_length=30, null=True)),
                ('confidence_score', models.FloatField(blank=True, help_text='AI confidence in sentiment analysis (0.0-1.0)', null=True)),
                ('urgency_score', models.FloatField(blank=True, help_text='AI-calculated urgency score (1.0-10.0)', null=True)),
                ('urgency_level', models.CharField(blank=True, choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], help_text='Calculated urgency category', max_length=20, null=True)),
                ('escalation_risk', models.FloatField(blank=True, help_text='Probability that this issue will escalate (0.0-1.0)', null=True)),
                ('community_impact_score', models.FloatField(blank=True, help_text='Estimated impact on community (1.0-10.0)', null=True)),
                ('affected_population_estimate', models.IntegerField(blank=True, help_text='Estimated number of people affected', null=True)),
                ('geographic_scope', models.CharField(blank=True, choices=[('individual', 'Individual'), ('household', 'Household'), ('neighborhood', 'Neighborhood'), ('ward', 'Ward'), ('sub_county', 'Sub County'), ('county', 'County'), ('multi_county', 'Multi County')], help_text='Geographic scope of the issue', max_length=20, null=True)),
                ('ai_category_suggestions', models.JSONField(default=dict, help_text='AI-suggested categories with confidence scores')),
                ('department_routing', models.JSONField(default=dict, help_text='Suggested department routing with priorities')),
                ('similar_feedback_ids', models.JSONField(default=list, help_text='IDs of similar feedback for pattern recognition')),
                ('language_detected', models.CharField(default='en', help_text='Detected primary language (en, sw, etc.)', max_length=10)),
                ('cultural_context', models.JSONField(default=dict, help_text='Cultural context indicators and local references')),
                ('communication_style', models.CharField(blank=True, choices=[('formal', 'Formal'), ('informal', 'Informal'), ('emotional', 'Emotional'), ('factual', 'Factual'), ('urgent', 'Urgent'), ('respectful', 'Respectful')], help_text='Detected communication style', max_length=20, null=True)),
                ('processing_version', models.CharField(default='1.0', help_text='AI model version used for analysis', max_length=10)),
                ('processed_at', models.DateTimeField(auto_now_add=True, help_text='When AI analysis was completed')),
                ('processing_time_seconds', models.FloatField(blank=True, help_text='Time taken for AI processing', null=True)),
                ('analysis_quality_score', models.FloatField(blank=True, help_text='Quality score of AI analysis (0.0-1.0)', null=True)),
                ('human_validated', models.BooleanField(default=False, help_text='Whether a human has validated this AI analysis')),
                ('validation_notes', models.TextField(blank=True, help_text='Human validation notes or corrections')),
                ('raw_ai_response', models.JSONField(default=dict, help_text='Complete raw response from AI for debugging')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='deleted_%(class)s_set', to=settings.AUTH_USER_MODEL)),
                ('feedback', models.OneToOneField(help_text='The feedback item this AI analysis belongs to', on_delete=django.db.models.deletion.CASCADE, related_name='ai_analysis', to='feedback.feedback')),
            ],
            options={
                'ordering': ['-processed_at'],
                'indexes': [models.Index(fields=['feedback', 'processed_at'], name='ai_feedback_feedbac_d44b7e_idx'), models.Index(fields=['urgency_level', 'escalation_risk'], name='ai_feedback_urgency_8d81ca_idx'), models.Index(fields=['sentiment_label', 'confidence_score'], name='ai_feedback_sentime_364852_idx'), models.Index(fields=['processing_version', 'analysis_quality_score'], name='ai_feedback_process_f4ad9c_idx'), models.Index(fields=['community_impact_score', 'affected_population_estimate'], name='ai_feedback_communi_8ee9d1_idx')],
            },
        ),
        migrations.CreateModel(
            name='AITrendAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_deleted', models.BooleanField(db_index=True, default=False)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('analysis_period_start', models.DateTimeField(help_text='Start of the analysis period')),
                ('analysis_period_end', models.DateTimeField(help_text='End of the analysis period')),
                ('analysis_type', models.CharField(choices=[('daily', 'Daily Analysis'), ('weekly', 'Weekly Analysis'), ('monthly', 'Monthly Analysis'), ('quarterly', 'Quarterly Analysis'), ('yearly', 'Yearly Analysis'), ('custom', 'Custom Period')], help_text='Type of trend analysis', max_length=30)),
                ('feedback_volume_trend', models.JSONField(default=dict, help_text='Feedback volume trends over time')),
                ('category_trends', models.JSONField(default=dict, help_text='Trends by feedback category')),
                ('sentiment_trends', models.JSONField(default=dict, help_text='Sentiment analysis trends')),
                ('urgency_trends', models.JSONField(default=dict, help_text='Urgency level trends')),
                ('location_trends', models.JSONField(default=dict, help_text='Geographic distribution trends')),
                ('predicted_issues', models.JSONField(default=list, help_text='AI-predicted emerging issues')),
                ('seasonal_patterns', models.JSONField(default=dict, help_text='Detected seasonal patterns')),
                ('anomalies_detected', models.JSONField(default=list, help_text='Unusual patterns or anomalies')),
                ('response_time_trends', models.JSONField(default=dict, help_text='Government response time trends')),
                ('resolution_rate_trends', models.JSONField(default=dict, help_text='Issue resolution rate trends')),
                ('citizen_satisfaction_trends', models.JSONField(default=dict, help_text='Inferred citizen satisfaction trends')),
                ('confidence_score', models.FloatField(blank=True, help_text='Overall confidence in trend analysis (0.0-1.0)', null=True)),
                ('data_quality_score', models.FloatField(blank=True, help_text='Quality of underlying data (0.0-1.0)', null=True)),
                ('sample_size', models.IntegerField(help_text='Number of feedback items analyzed')),
                ('generated_by_model', models.CharField(help_text='AI model used for trend analysis', max_length=50)),
                ('generation_parameters', models.JSONField(default=dict, help_text='Parameters used for analysis')),
                ('processing_time_minutes', models.FloatField(blank=True, help_text='Time taken to generate analysis', null=True)),
                ('county', models.ForeignKey(help_text='County this trend analysis covers', on_delete=django.db.models.deletion.CASCADE, related_name='ai_trend_analyses', to='users.county')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='deleted_%(class)s_set', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-analysis_period_end', '-created_at'],
                'indexes': [models.Index(fields=['county', 'analysis_type', 'analysis_period_end'], name='ai_aitrenda_county__6ecf69_idx'), models.Index(fields=['analysis_period_start', 'analysis_period_end'], name='ai_aitrenda_analysi_8118a1_idx'), models.Index(fields=['confidence_score', 'data_quality_score'], name='ai_aitrenda_confide_64c6bf_idx'), models.Index(fields=['created_at', 'generated_by_model'], name='ai_aitrenda_created_75cd42_idx')],
                'unique_together': {('county', 'analysis_type', 'analysis_period_start', 'analysis_period_end')},
            },
        ),
        migrations.CreateModel(
            name='AIResponseSuggestion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_deleted', models.BooleanField(db_index=True, default=False)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('suggested_content', models.TextField(help_text='AI-generated response content')),
                ('response_type', models.CharField(choices=[('acknowledgment', 'Acknowledgment'), ('information_request', 'Information Request'), ('action_plan', 'Action Plan'), ('status_update', 'Status Update'), ('resolution', 'Resolution'), ('escalation', 'Escalation'), ('detailed_explanation', 'Detailed Explanation')], help_text='Type of response being suggested', max_length=30)),
                ('empathy_score', models.FloatField(blank=True, help_text='AI-assessed empathy level of response (0.0-1.0)', null=True)),
                ('clarity_score', models.FloatField(blank=True, help_text='AI-assessed clarity of response (0.0-1.0)', null=True)),
                ('actionability_score', models.FloatField(blank=True, help_text='How actionable/helpful the response is (0.0-1.0)', null=True)),
                ('appropriateness_score', models.FloatField(blank=True, help_text='Cultural and contextual appropriateness (0.0-1.0)', null=True)),
                ('target_audience', models.CharField(choices=[('citizen', 'Individual Citizen'), ('community', 'Community Group'), ('media', 'Media/Press'), ('official', 'Government Official')], default='citizen', help_text='Target audience for this response', max_length=30)),
                ('tone', models.CharField(blank=True, choices=[('formal', 'Formal'), ('friendly', 'Friendly'), ('empathetic', 'Empathetic'), ('professional', 'Professional'), ('urgent', 'Urgent'), ('reassuring', 'Reassuring')], help_text='Recommended tone for the response', max_length=20, null=True)),
                ('times_used', models.IntegerField(default=0, help_text='Number of times this suggestion was used')),
                ('effectiveness_rating', models.FloatField(blank=True, help_text='User-rated effectiveness (1.0-5.0)', null=True)),
                ('user_modifications', models.JSONField(default=list, help_text='Track how users modify AI suggestions')),
                ('generation_model', models.CharField(help_text='AI model used to generate this response', max_length=50)),
                ('generation_parameters', models.JSONField(default=dict, help_text='Parameters used for AI generation')),
                ('generated_at', models.DateTimeField(auto_now_add=True, help_text='When this suggestion was generated')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='deleted_%(class)s_set', to=settings.AUTH_USER_MODEL)),
                ('feedback', models.ForeignKey(help_text='The feedback this response suggestion is for', on_delete=django.db.models.deletion.CASCADE, related_name='ai_response_suggestions', to='feedback.feedback')),
            ],
            options={
                'ordering': ['-effectiveness_rating', '-generated_at'],
                'indexes': [models.Index(fields=['feedback', 'response_type'], name='ai_airespon_feedbac_ed75e0_idx'), models.Index(fields=['effectiveness_rating', 'times_used'], name='ai_airespon_effecti_8d8f8e_idx'), models.Index(fields=['target_audience', 'tone'], name='ai_airespon_target__650491_idx'), models.Index(fields=['generated_at', 'generation_model'], name='ai_airespon_generat_128f9b_idx')],
            },
        ),
        migrations.CreateModel(
            name='AIProcessingLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_deleted', models.BooleanField(db_index=True, default=False)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('task_id', models.CharField(db_index=True, help_text='Celery task ID for tracking', max_length=100)),
                ('task_name', models.CharField(help_text='Name of the AI task executed', max_length=100)),
                ('status', models.CharField(choices=[('started', 'Started'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed'), ('retrying', 'Retrying'), ('timeout', 'Timeout')], default='started', help_text='Current processing status', max_length=20)),
                ('start_time', models.DateTimeField(auto_now_add=True, help_text='When processing started')),
                ('end_time', models.DateTimeField(blank=True, help_text='When processing completed', null=True)),
                ('duration_seconds', models.FloatField(blank=True, help_text='Total processing time', null=True)),
                ('memory_usage_mb', models.FloatField(blank=True, help_text='Peak memory usage during processing', null=True)),
                ('api_calls_made', models.IntegerField(default=0, help_text='Number of API calls to LLM services')),
                ('tokens_consumed', models.IntegerField(default=0, help_text='Total tokens consumed by LLM APIs')),
                ('error_message', models.TextField(blank=True, help_text='Error message if processing failed')),
                ('retry_count', models.IntegerField(default=0, help_text='Number of retry attempts')),
                ('results_summary', models.JSONField(default=dict, help_text='Summary of processing results')),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='deleted_%(class)s_set', to=settings.AUTH_USER_MODEL)),
                ('feedback', models.ForeignKey(blank=True, help_text='Feedback item processed (if applicable)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='ai_processing_logs', to='feedback.feedback')),
            ],
            options={
                'ordering': ['-start_time'],
                'indexes': [models.Index(fields=['task_id', 'status'], name='ai_aiproces_task_id_182e93_idx'), models.Index(fields=['task_name', 'start_time'], name='ai_aiproces_task_na_176b2e_idx'), models.Index(fields=['feedback', 'status'], name='ai_aiproces_feedbac_a369fe_idx'), models.Index(fields=['start_time', 'duration_seconds'], name='ai_aiproces_start_t_5b3a0f_idx')],
            },
        ),
    ]
