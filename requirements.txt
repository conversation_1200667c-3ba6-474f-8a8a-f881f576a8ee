amqp==5.3.1
annotated-types==0.7.0
anthropic==0.40.0
anyio==4.10.0
asgiref==3.9.1
async-timeout==5.0.1
attrs==25.3.0
autopep8==2.3.2
bcrypt==4.3.0
billiard==4.2.1
celery==5.3.4
certifi==2025.8.3
cffi==1.17.1
channels==4.0.0
channels-redis==4.1.0
charset-normalizer==3.4.3
click==8.1.8
click-didyoumean==0.3.1
click-plugins==*******
click-repl==0.3.0
cron_descriptor==2.0.5
cryptography==41.0.7
distro==1.9.0
dj-database-url==3.0.1
Django==4.2.23
django-celery-beat==2.7.0
django-celery-results==2.5.1
django-cors-headers==4.3.1
django-debug-toolbar==6.0.0
django-extensions==4.1
django-filter==23.3
django-redis==5.4.0
django-silk==5.3.0
django-timezone-field==7.1
djangorestframework==3.14.0
djangorestframework-simplejwt==5.3.0
drf-spectacular==0.28.0
exceptiongroup==1.3.0
filelock==3.19.1
flower==2.0.1
fsspec==2025.7.0
gprof2dot==2025.4.14
gunicorn==23.0.0
h11==0.16.0
hf-xet==1.1.9
httpcore==1.0.9
httpx==0.28.1
huggingface-hub==0.34.4
humanize==4.13.0
idna==3.10
importlib_metadata==8.7.0
inflection==0.5.1
Jinja2==3.1.6
jiter==0.10.0
joblib==1.5.2
jsonschema==4.25.0
jsonschema-specifications==2025.4.1
kombu==5.5.4
langdetect==1.0.9
Markdown==3.8.2
MarkupSafe==3.0.2
mpmath==1.3.0
msgpack==1.1.1
networkx==3.2.1
nltk==3.8.1
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.8.90
nvidia-cuda-nvrtc-cu12==12.8.93
nvidia-cuda-runtime-cu12==12.8.90
nvidia-cudnn-cu12==9.10.2.21
nvidia-cufft-cu12==11.3.3.83
nvidia-cufile-cu12==1.13.1.3
nvidia-curand-cu12==10.3.9.90
nvidia-cusolver-cu12==11.7.3.90
nvidia-cusparse-cu12==12.5.8.93
nvidia-cusparselt-cu12==0.7.1
nvidia-nccl-cu12==2.27.3
nvidia-nvjitlink-cu12==12.8.93
nvidia-nvtx-cu12==12.8.90
openai==1.102.0
packaging==25.0
pgvector==0.4.1
phonenumbers==8.12.42
Pillow>=10.4.0
prometheus_client==0.22.1
prompt_toolkit==3.0.52
psutil==7.0.0
psycopg2-binary==2.9.10
pycodestyle==2.14.0
pycparser==2.22
pydantic==2.11.7
pydantic_core==2.33.2
PyJWT==2.10.1
PyMuPDFb==1.23.22
PyPDF2==3.0.1
python-crontab==3.3.0
python-dateutil==2.9.0.post0
python-decouple==3.8
python-dotenv==1.0.1
pytz==2025.2
PyYAML==6.0.2
redis==4.6.0
referencing==0.36.2
regex==2025.7.34
requests==2.32.5
rpds-py==0.26.0
safetensors==0.6.2
scikit-learn==1.6.1
sentence-transformers==5.1.0
six==1.17.0
sniffio==1.3.1
sqlparse==0.5.3
sympy==1.14.0
threadpoolctl==3.6.0
tokenizers==0.22.0
tomli==2.2.1
torch==2.8.0
tornado==6.5.2
tqdm==4.67.1
transformers==4.56.0
triton==3.4.0
typing-inspection==0.4.1
typing_extensions==4.14.1
tzdata==2025.2
uritemplate==4.2.0
urllib3==2.5.0
vine==5.1.0
wcwidth==0.2.13
websocket-client==1.8.0
whitenoise==6.9.0
zipp==3.23.0
